# 积分过期定时任务设置说明

## 📋 功能说明

积分过期处理定时任务用于自动处理用户的过期积分，确保积分系统的正常运行。

### 🎯 处理逻辑
- **过期时间**：积分在获得后的次年12月31日23:59:59过期
- **自动扣除**：每天自动检查并扣除过期积分
- **记录日志**：完整记录积分扣除过程
- **事务安全**：使用数据库事务确保数据一致性

## 🔧 使用方法

### 1. 手动执行命令
```bash
# 进入项目根目录
cd /path/to/your/project

# 执行积分过期处理
php think score:expire
```

### 2. 设置定时任务（推荐）

#### Linux/Unix 系统 (crontab)
```bash
# 编辑定时任务
crontab -e

# 添加以下行（每天凌晨1点执行）
0 1 * * * cd /www/wwwroot/mi.xiniu.wedomi.cn && php think score:expire

# 或者添加日志记录
0 1 * * * cd /www/wwwroot/mi.xiniu.wedomi.cn && php think score:expire >> /var/log/score_expire.log 2>&1
```

#### Windows 系统 (任务计划程序)
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器：每天凌晨1点
4. 设置操作：启动程序
   - 程序：`php`
   - 参数：`think score:expire`
   - 起始位置：`E:\www\202502\mi.xiniu.wedomi.cn_C4fmT`

### 3. 手动调用存储过程（备用）
```sql
-- 直接在数据库中执行
CALL ProcessExpiredScores();
```

## 📊 监控和日志

### 查看处理结果
```bash
# 查看系统日志
tail -f runtime/log/202X/XX/XX.log | grep "积分过期"
```

### 数据库查询
```sql
-- 查看过期处理历史
SELECT * FROM fa_shopro_user_wallet_log 
WHERE event = 'score_expire' 
ORDER BY createtime DESC 
LIMIT 10;

-- 查看即将过期的积分（7天内）
SELECT 
    COUNT(DISTINCT user_id) as user_count,
    SUM(amount) as total_score
FROM fa_shopro_user_wallet_log 
WHERE type = 'score' 
    AND amount > 0 
    AND expire_time <= UNIX_TIMESTAMP() + (7 * 24 * 3600)
    AND expire_time > UNIX_TIMESTAMP()
    AND expire_time IS NOT NULL;

-- 查看已过期但未处理的积分
SELECT 
    COUNT(DISTINCT user_id) as user_count,
    SUM(amount) as total_score
FROM fa_shopro_user_wallet_log 
WHERE type = 'score' 
    AND amount > 0 
    AND expire_time <= UNIX_TIMESTAMP()
    AND expire_time > 0;
```

## ⚠️ 注意事项

### 1. 执行时间建议
- **推荐时间**：凌晨1-3点（用户访问量较少）
- **避免时间**：业务高峰期（避免影响系统性能）

### 2. 权限要求
- PHP命令行执行权限
- 数据库读写权限
- 日志文件写入权限

### 3. 错误处理
- 命令执行失败会记录错误日志
- 数据库操作使用事务，确保数据一致性
- 支持重复执行，不会重复处理已过期的积分

### 4. 性能考虑
- 大量用户时建议分批处理
- 可以考虑在业务低峰期执行
- 定期清理过期的日志记录

## 🔍 故障排查

### 常见问题

1. **命令不存在**
   ```bash
   # 检查命令是否注册
   php think list | grep score
   ```

2. **权限问题**
   ```bash
   # 检查文件权限
   ls -la addons/shopro/command/ScoreExpire.php
   ```

3. **数据库连接问题**
   ```bash
   # 检查数据库配置
   php think config database
   ```

### 调试模式
```bash
# 开启调试模式执行
php think score:expire --debug
```

## 📈 扩展功能

### 1. 邮件通知
可以在命令中添加邮件通知功能，当处理大量过期积分时发送通知。

### 2. 统计报告
定期生成积分过期统计报告，分析用户积分使用情况。

### 3. 预警机制
在积分即将过期前（如7天）向用户发送提醒。

## 📝 更新日志

- **2025-01-27**：创建积分过期处理命令
- **功能**：支持PHP和存储过程两种处理方式
- **特性**：完整的事务处理和错误日志记录
