# 会员等级系统集成说明

## 📋 系统概述

会员等级系统已成功创建，基于消费分的等级划分，支持免邮权益、折扣权益和转盘权益。系统采用配置化设计，所有权益参数都可以在后台灵活调整。

## 🎯 等级体系

| 等级 | 消费分要求 | 免邮权益 | 折扣权益 | 转盘权益 |
|------|-----------|---------|---------|---------|
| VIP1 | 1000 | 免5KG快递费1次/月 | 无 | +1次/月 |
| VIP2 | 3000 | 免5KG快递费或送货费2次/月 | 无 | +2次/月 |
| VIP3 | 8000 | 免5KG快递费或送货费3次/月 | 无 | +3次/月 |
| VIP4 | 15000 | 免5KG快递费或送货费3次/月 | 97折 | +3次/月 |
| VIP5 | 30000 | 免5KG快递费或送货费无限次 | 97折 | +4次/月 |

## 🗄️ 数据表结构

### 1. fa_shopro_member_level (会员等级配置表)
- 存储等级配置信息
- 支持权益配置JSON格式
- 支持后台管理和修改

### 2. fa_shopro_user_member (用户会员记录表)
- 记录用户当前等级和消费分
- 自动计算升级进度
- 支持等级过期设置

### 3. fa_shopro_member_benefit_log (权益使用记录表)
- 按月记录权益使用情况
- 支持权益次数限制
- 自动过期清理

### 4. fa_shopro_member_upgrade_log (升级记录表)
- 记录等级升级历史
- 支持多种触发方式
- 便于数据分析

## 🔧 核心功能

### 1. 消费分更新
```php
// 用户充值或消费时调用
use addons\shopro\service\MemberService;

// 充值更新消费分
MemberService::updateConsumeScore($userId, $amount, 'recharge', $orderId);

// 消费更新消费分
MemberService::updateConsumeScore($userId, $amount, 'consume', $orderId);
```

### 2. 免邮权益检查
```php
// 下单时检查免邮权益
$result = MemberService::checkAndUseFreeShipping($userId, $orderId, 'express', $weight);

if ($result['success']) {
    // 可以免邮，减免运费
    $shippingFee = 0;
} else {
    // 不能免邮，显示原因
    echo $result['message'];
}
```

### 3. 折扣率获取
```php
// 计算订单金额时获取会员折扣
$discountRate = MemberService::getUserDiscountRate($userId);
$finalAmount = $originalAmount * ($discountRate / 100);
```

### 4. 转盘次数获取
```php
// 转盘抽奖时获取额外次数
$extraCount = MemberService::getUserLotteryExtraCount($userId);
$totalCount = $baseCount + $extraCount;
```

## 📱 前端集成

### 1. 个人中心入口
在个人中心页面添加会员等级入口：
```vue
<view class="menu-item" @tap="goToMemberLevel">
  <text class="iconfont icon-vip"></text>
  <text>会员等级</text>
  <text class="level-badge">VIP2</text>
</view>
```

### 2. 页面跳转
```javascript
goToMemberLevel() {
  uni.navigateTo({
    url: '/pages/member/level'
  })
}
```

### 3. API调用示例
```javascript
// 获取会员信息
const memberInfo = await this.$api.get('/shopro/member/index')

// 获取权益使用情况
const benefits = await this.$api.get('/shopro/member/benefits')

// 获取升级历史
const history = await this.$api.get('/shopro/member/upgradeHistory')
```

## 🔌 业务集成点

### 1. 用户注册
```php
// 用户注册成功后初始化会员记录
$userMember = UserMember::getMemberByUserId($userId);
```

### 2. 充值流程
```php
// 充值成功后更新消费分
MemberService::updateConsumeScore($userId, $amount, 'recharge', $rechargeId);
```

### 3. 下单流程
```php
// 计算运费时检查免邮权益
$freeShippingResult = MemberService::checkAndUseFreeShipping($userId, $orderId, $shippingType, $weight);

// 计算订单金额时应用会员折扣
$discountRate = MemberService::getUserDiscountRate($userId);
$discountAmount = $originalAmount * (100 - $discountRate) / 100;
```

### 4. 支付完成
```php
// 支付完成后更新消费分
MemberService::updateConsumeScore($userId, $payAmount, 'consume', $orderId);
```

### 5. 转盘抽奖
```php
// 获取用户转盘次数
$baseCount = 3; // 基础次数
$extraCount = MemberService::getUserLotteryExtraCount($userId);
$totalCount = $baseCount + $extraCount;
```

## 🎛️ 后台管理

### 1. 等级配置管理
- 通过FastAdmin自动生成的CRUD界面
- 可以调整消费分要求
- 可以修改权益配置

### 2. 用户会员管理
- 查看用户等级信息
- 手动调整用户等级
- 查看升级历史

### 3. 权益使用统计
- 查看权益使用情况
- 分析会员活跃度
- 生成统计报表

## ⚙️ 配置说明

### 权益配置JSON格式
```json
{
  "free_shipping": {
    "count": 2,           // 每月次数，999表示无限次
    "weight_limit": 5,    // 重量限制(KG)
    "type": "all"         // express:仅快递, delivery:仅送货, all:全部
  },
  "lottery": {
    "extra_count": 2      // 额外转盘次数
  },
  "discount": {
    "rate": 97            // 折扣率，97表示97折
  }
}
```

### 触发类型说明
- `consume`: 消费升级
- `recharge`: 充值升级  
- `manual`: 手动调整
- `system`: 系统调整

## 🔍 监控和维护

### 1. 定期任务
```php
// 清理过期权益记录
MemberService::cleanExpiredBenefits();

// 获取会员统计
$stats = MemberService::getMemberLevelStats();
```

### 2. 数据查询
```sql
-- 查看等级分布
SELECT level, COUNT(*) as count FROM fa_shopro_user_member GROUP BY level;

-- 查看权益使用情况
SELECT benefit_type, SUM(used_count) as total_used 
FROM fa_shopro_member_benefit_log 
WHERE month = '2025-01' 
GROUP BY benefit_type;

-- 查看升级趋势
SELECT DATE(FROM_UNIXTIME(createtime)) as date, COUNT(*) as upgrades 
FROM fa_shopro_member_upgrade_log 
WHERE createtime >= UNIX_TIMESTAMP('2025-01-01') 
GROUP BY date;
```

## 🚀 扩展建议

### 1. 生日特权
- 生日月额外权益
- 生日专属优惠券

### 2. 邀请奖励
- 邀请好友获得消费分
- 被邀请用户首单优惠

### 3. 积分兑换
- 消费分兑换商品
- 消费分兑换优惠券

### 4. 等级保级
- 设置等级有效期
- 消费分不足自动降级

## 📞 技术支持

如有问题，请检查：
1. 数据表是否正确创建
2. 模型类是否正确加载
3. API接口是否正常响应
4. 前端页面是否正确显示

系统已完全按照FastAdmin规范设计，支持后台一键生成管理界面，具备完整的会员等级功能。
